#!/usr/bin/env python3
"""
OTIO → VideoSplitter YAML
- Dependencies: pip install OpenTimelineIO PyYAML
- Works with Kdenlive: File → Export → OpenTimelineIO (.otio)
- Also reads .kdenlive if 'otio-kdenlive-adapter' is installed.

Usage:
  # Use constants below (no args)
  python otio_to_videosplitter_yaml.py

  # Or CLI overrides
  python otio_to_videosplitter_yaml.py edit.otio -o timecodes.yaml --filter-source "Perspektiv_merged"
"""

from __future__ import annotations
import argparse, sys
from dataclasses import dataclass
from decimal import Decimal, ROUND_DOWN
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional

try:
    import opentimelineio as otio
except Exception as e:
    print("OpenTimelineIO not installed. Run: pip install OpenTimelineIO", file=sys.stderr)
    raise

import yaml


# ------------------------------- CONFIG ---------------------------------------
@dataclass
class Constants:
    # Execution
    USE_CLI_IF_ARGS: bool = True        # If args provided, use CLI. Else use constants.
    INPUT_PATH: str = "edit.otio"       # .otio preferred. .kdenlive supported via adapter.
    OUTPUT_YAML: str = "timecodes.yaml"
    FILTER_SOURCE_SUBSTR: Optional[str] = None  # e.g., "Perspektiv_merged" or None
    FAIL_IF_MIXED_SOURCES: bool = False # If True and >1 source detected, abort.

    # Output YAML defaults for your VideoSplitter
    OUTPUT_DIR: str = "exports"
    USE_SUBDIR: bool = True
    OUTPUT_FORMAT: str = "mp4"
    CODEC_PROFILE: str = "h264_intra"
    QUALITY: int = 18
    THREADS: int = 4
    APPEND_TIMESTAMPS: bool = True
    INCLUDE_SOURCE_PREFIX: bool = False

    # Segment emission
    EMIT_SOURCE_IN_SEGMENTS: bool = False  # set True if you want 'source' per segment


# ----------------------------- CORE LOGIC -------------------------------------
def _fmt_rtime(rt: otio.opentime.RationalTime) -> str:
    """HH:MM:SS.mmm, floor to ms (no roundup)."""
    s = Decimal(str(rt.to_seconds())).quantize(Decimal('0.001'), rounding=ROUND_DOWN)
    total_ms = int(s * 1000)
    h, rem = divmod(total_ms, 3600000)
    m, rem = divmod(rem, 60000)
    sec, ms = divmod(rem, 1000)
    return f"{h:02d}:{m:02d}:{sec:02d}" + (f".{ms:03d}" if ms else "")


def _path_from_target_url(url: str) -> Path:
    """Resolve OTIO ExternalReference target_url to local filesystem Path."""
    try:
        u = otio.url_utils.urlparse(url)
        if u.scheme in ("", "file"):
            p = otio.url_utils.unquote(u.path)
            # Windows file:///C:/... → strip leading slash
            if len(p) >= 3 and p[0] == "/" and p[2] == ":":
                p = p.lstrip("/")
            return Path(p)
    except Exception:
        pass
    return Path(url)


def collect_segments(otio_path: str, source_filter: Optional[str]) -> Tuple[List[Dict[str, Any]], List[str]]:
    tl = otio.adapters.read_from_file(otio_path)
    segs: List[Dict[str, Any]] = []
    sources = set()

    for track in tl.tracks:
        if track.kind != otio.schema.TrackKind.Video:
            continue
        for clip in track.each_clip():
            mr = clip.media_reference
            if not isinstance(mr, otio.schema.ExternalReference):
                continue
            src_path = _path_from_target_url(mr.target_url)
            src_str = str(src_path)
            if source_filter and source_filter.lower() not in src_str.lower():
                continue

            rng = clip.trimmed_range() or clip.source_range
            if not rng or rng.duration.value <= 0:
                continue

            start = _fmt_rtime(rng.start_time)
            end = _fmt_rtime(rng.start_time + rng.duration)
            title = clip.name or f"clip_{len(segs)+1}"

            seg_item = {"start": start, "end": end, "title": title}
            seg_item["source"] = src_str
            segs.append(seg_item)
            sources.add(src_str)

    return segs, sorted(sources)


def make_yaml_payload(
    segs: List[Dict[str, Any]],
    sources: List[str],
    const: Constants
) -> Dict[str, Any]:
    if not segs:
        raise SystemExit("No segments found in OTIO.")

    if const.FAIL_IF_MIXED_SOURCES and len(sources) > 1:
        raise SystemExit(f"Multiple sources detected ({len(sources)}). Set FILTER_SOURCE_SUBSTR or disable FAIL_IF_MIXED_SOURCES.")

    single_source = (len(sources) == 1)
    input_video = sources[0] if single_source else ("MIXED" if not const.FAIL_IF_MIXED_SOURCES else "")

    out_segments = []
    for s in segs:
        item = {"start": s["start"], "end": s["end"], "title": s["title"]}
        if const.EMIT_SOURCE_IN_SEGMENTS:
            item["source"] = s["source"]
        out_segments.append(item)

    data = {
        "input_video": input_video,
        "use_subdirectory": const.USE_SUBDIR,
        "output_dir": const.OUTPUT_DIR,
        "ffmpeg_path": "ffmpeg",
        "ffprobe_path": "ffprobe",
        "output_format": const.OUTPUT_FORMAT,
        "codec_profile": const.CODEC_PROFILE,
        "quality": const.QUALITY,
        "threads": const.THREADS,
        "append_timestamps": const.APPEND_TIMESTAMPS,
        "include_source_prefix": const.INCLUDE_SOURCE_PREFIX,
        "segments": out_segments,
    }
    return data


def write_yaml(payload: Dict[str, Any], out_path: str) -> None:
    with open(out_path, "w", encoding="utf-8") as f:
        yaml.safe_dump(payload, f, sort_keys=False, allow_unicode=True)


# ------------------------------ ENTRY POINT -----------------------------------
def run_with_constants(const: Constants) -> None:
    segs, sources = collect_segments(const.INPUT_PATH, const.FILTER_SOURCE_SUBSTR)
    payload = make_yaml_payload(segs, sources, const)
    write_yaml(payload, const.OUTPUT_YAML)
    print(f"Wrote {const.OUTPUT_YAML} with {len(payload['segments'])} segments."
          f" Source(s): {', '.join(sources) if sources else '—'}")


def main() -> None:
    if Constants.USE_CLI_IF_ARGS and len(sys.argv) > 1:
        ap = argparse.ArgumentParser(description="Convert OTIO/.kdenlive to VideoSplitter YAML")
        ap.add_argument("input", help=".otio preferred; .kdenlive supported if adapter installed")
        ap.add_argument("-o", "--output", default=Constants.OUTPUT_YAML)
        ap.add_argument("--filter-source", default=Constants.FILTER_SOURCE_SUBSTR)
        ap.add_argument("--fail-if-mixed", action="store_true", default=Constants.FAIL_IF_MIXED_SOURCES)
        ap.add_argument("--emit-source", action="store_true", default=Constants.EMIT_SOURCE_IN_SEGMENTS)
        args = ap.parse_args()

        const = Constants(
            INPUT_PATH=args.input,
            OUTPUT_YAML=args.output,
            FILTER_SOURCE_SUBSTR=args.filter_source,
            FAIL_IF_MIXED_SOURCES=bool(args.fail_if_mixed),
            EMIT_SOURCE_IN_SEGMENTS=bool(args.emit_source),
            # keep other defaults
        )
        run_with_constants(const)
    else:
        run_with_constants(Constants())


if __name__ == "__main__":
    main()


# 1) Export .otio from Kdenlive
# 2) Convert to your YAML
# python convert_kdenlive_otio.py 2025.01.22--project.002.otio -o 2025-01-22.yaml --filter-source
